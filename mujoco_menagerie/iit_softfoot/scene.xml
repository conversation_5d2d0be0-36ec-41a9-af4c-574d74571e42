<!-- This example scene attaches to a foot to a simple loading test rig, demonstrating
the foot's compliance against an obstacle.-->
<mujoco model="softfoot scene">
  <statistic center="0 0 0.1" extent=".3"/>

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
    <rgba haze="0.15 0.25 0.35 1"/>
    <global azimuth="150" elevation="-20"/>
  </visual>

  <asset>
    <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0" width="512" height="3072"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3"
      markrgb="0.8 0.8 0.8" width="300" height="300"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2"/>
    <model name="softfoot_model" file="softfoot.xml"/>
  </asset>

  <worldbody>
    <light pos="0 0 3" dir="0 0 -1" directional="false"/>
    <geom name="floor" size="0 0 .125" type="plane" material="groundplane" conaffinity="15" condim="3"/>
    <body name="sf_origin" pos="0 0 0.2">
      <joint name="load" type="slide" damping="100" axis="0 0 -1"/>
      <attach model="softfoot_model" body="attachment_cube" prefix="sf_"/>
    </body>
    <geom type="cylinder" euler="90 0 0" rgba="0.8 0.7 0.2 1" size="0.025 0.1"/>
  </worldbody>

  <actuator>
    <position joint="load" kp="1000" ctrllimited="true" ctrlrange="0 0.3"/>
  </actuator>
</mujoco>
