<mujoco model="tiago_dual">

  <include file="tiago_dual.xml"/>

  <default>
    <default class="position">
      <position dampratio="1"/>
    </default>
  </default>

  <actuator>
    <!-- Omni base actuators -->
    <velocity name="wheel_front_right_joint_velocity" joint="wheel_front_right_joint" ctrlrange="-5 5" kv="2000" />
    <velocity name="wheel_front_left_joint_velocity" joint="wheel_front_left_joint" ctrlrange="-5 5" kv="2000" />
    <velocity name="wheel_rear_right_joint_velocity" joint="wheel_rear_right_joint" ctrlrange="-5 5" kv="2000" />
    <velocity name="wheel_rear_left_joint_velocity" joint="wheel_rear_left_joint" ctrlrange="-5 5" kv="2000" />

    <!-- Torso actuator -->
    <position class="position" name="torso_lift_joint_position" joint="torso_lift_joint" kp="50000" inheritrange="0.98"/>
    <!-- Head actuators -->
    <position class="position" name="head_1_joint_position" joint="head_1_joint" kp="20" inheritrange="0.93"/>
    <position class="position" name="head_2_joint_position" joint="head_2_joint" kp="20" inheritrange="0.93"/>

    <!-- Left arm actuators -->
    <position class="position" name="arm_left_1_joint_position" joint="arm_left_1_joint" inheritrange="1.0" kp="250"/>
    <position class="position" name="arm_left_2_joint_position" joint="arm_left_2_joint" inheritrange="1.0" kp="1500"/>
    <position class="position" name="arm_left_3_joint_position" joint="arm_left_3_joint" inheritrange="1.0" kp="600"/>
    <position class="position" name="arm_left_4_joint_position" joint="arm_left_4_joint" inheritrange="1.0" kp="1000"/>
    <position class="position" name="arm_left_5_joint_position" joint="arm_left_5_joint" inheritrange="1.0" kp="80"/>
    <position class="position" name="arm_left_6_joint_position" joint="arm_left_6_joint" inheritrange="1.0" kp="80"/>
    <position class="position" name="arm_left_7_joint_position" joint="arm_left_7_joint" inheritrange="1.0" kp="80"/>
    <position class="position" name="gripper_left_left_finger_joint_position" joint="gripper_left_left_finger_joint" kp="500" inheritrange="0.999" forcerange="-5.197 5.197"/>
    <position class="position" name="gripper_left_right_finger_joint_position" joint="gripper_left_right_finger_joint" kp="500" inheritrange="0.999" forcerange="-5.197 5.197"/>

    <!-- Right arm actuators -->
    <position class="position" name="arm_right_1_joint_position" joint="arm_right_1_joint" inheritrange="1.0" kp="250"/>
    <position class="position" name="arm_right_2_joint_position" joint="arm_right_2_joint" inheritrange="1.0" kp="1500"/>
    <position class="position" name="arm_right_3_joint_position" joint="arm_right_3_joint" inheritrange="1.0" kp="600"/>
    <position class="position" name="arm_right_4_joint_position" joint="arm_right_4_joint" inheritrange="1.0" kp="1000"/>
    <position class="position" name="arm_right_5_joint_position" joint="arm_right_5_joint" inheritrange="1.0" kp="80"/>
    <position class="position" name="arm_right_6_joint_position" joint="arm_right_6_joint" inheritrange="1.0" kp="80"/>
    <position class="position" name="arm_right_7_joint_position" joint="arm_right_7_joint" inheritrange="1.0" kp="80"/>
    <position class="position" name="gripper_right_left_finger_joint_position" joint="gripper_right_left_finger_joint" kp="500" inheritrange="0.999" forcerange="-5.197 5.197"/>
    <position class="position" name="gripper_right_right_finger_joint_position" joint="gripper_right_right_finger_joint" kp="500" inheritrange="0.999" forcerange="-5.197 5.197"/>
  </actuator>

</mujoco>
