# Description

<!-- Briefly describe what this PR does -->

Fixes: <!-- List any GitHub issues this PR addresses -->

# Checklist

Please check off each item (`[x]`) once complete, or mark it as `[N/A]` if it doesn't apply:

- [ ] Added your name to `CONTRIBUTORS.md` (alphabetically by first name)
- [ ] Updated `CHANGELOG.md`:
  - [ ] Global changelog (if your change affects the overall repo)
  - [ ] Model-specific changelog (if it affects a specific model only)
- [ ] Followed the XML formatting/style guidelines (if editing MJCF)
- [ ] Ran `pytest test/` locally and ensured all tests pass
- [ ] Signed the [Contributor License Agreement (CLA)](https://cla.developers.google.com/)

Refer to the [contributing guide](https://github.com/google-deepmind/mujoco_menagerie/blob/main/CONTRIBUTING.md) if you're unsure about any of the steps.
