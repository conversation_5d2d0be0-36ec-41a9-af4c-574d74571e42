import mujoco
import mujoco.viewer
import numpy as np
import time

# Load the Apollo robot model from the MuJoCo Menagerie
model_path = "mujoco_menagerie/apptronik_apollo/scene.xml"

print(f"Loading Apollo robot model from: {model_path}")

# Load the model from XML file
model = mujoco.MjModel.from_xml_path(model_path)
data = mujoco.MjData(model)

print(f"Model loaded successfully!")
print(f"Number of degrees of freedom: {model.nv}")
print(f"Number of actuators: {model.nu}")
print(f"Number of bodies: {model.nbody}")

# Initialize the robot in a standing position
# Reset to default position
mujoco.mj_resetData(model, data)

# Create a viewer and run the simulation
print("Starting Apollo robot simulation...")
with mujoco.viewer.launch_passive(model, data) as viewer:
    # Simulate for a longer time to observe the robot
    start = time.time()
    simulation_time = 30  # Run for 30 seconds

    while viewer.is_running() and time.time() - start < simulation_time:
        # Apply some gentle perturbations to make the simulation interesting
        current_time = time.time() - start

        # Add small random forces to some joints to create natural movement
        if model.nu > 0:  # If there are actuators
            # Apply small sinusoidal torques to create gentle swaying motion
            for i in range(min(6, model.nu)):  # Apply to first 6 actuators (torso/hip area)
                data.ctrl[i] = 0.1 * np.sin(current_time * 0.5 + i) * np.random.uniform(0.5, 1.0)

        # Step the simulation
        mujoco.mj_step(model, data)
        viewer.sync()

        # Add small delay for real-time viewing
        time.sleep(0.01)

print("Apollo simulation completed!")
