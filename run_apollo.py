import mujoco
import mujoco.viewer
import numpy as np
import time

# Define a more complex model with multiple objects
XML = """
<mujoco>
    <option gravity="0 0 -9.81"/>
    <worldbody>
        <light pos="0 0 3" dir="0 0 -1"/>
        <geom type="plane" size="2 2 0.1" rgba="0.9 0.9 0.9 1"/>
        <body name="box" pos="0 0 2">
            <joint type="free"/>
            <geom type="box" size="0.15 0.15 0.15" rgba="1 0.2 0.2 1" mass="1"/>
        </body>
        <body name="sphere" pos="0.5 0.5 1">
            <joint type="free"/>
            <geom type="sphere" size="0.1" rgba="0.2 0.2 1 1" mass="0.5"/>
        </body>
    </worldbody>
</mujoco>
"""

# Load the model from XML string
model = mujoco.MjModel.from_xml_string(XML)
data = mujoco.MjData(model)

# Initialize some state
data.qpos[0] = 0  # x position of box
data.qpos[1] = 0  # y position of box
data.qpos[2] = 2  # z position of box

# Create a viewer and run the simulation
with mujoco.viewer.launch_passive(model, data) as viewer:
    # Simulate for a fixed time
    start = time.time()
    while viewer.is_running() and time.time() - start < 10:
        # Apply some forces for interesting dynamics
        data.qfrc_applied[2] = np.sin(time.time()) * 5  # Varying force in z direction
        
        # Step the simulation
        mujoco.mj_step(model, data)
        viewer.sync()
        
        # Add small delay for real-time viewing
        time.sleep(0.01)
