import mujoco
import mujoco.viewer
import numpy as np
import time

def main():
    # Load the Apollo robot model from the MuJoCo Menagerie
    model_path = "mujoco_menagerie/apptronik_apollo/scene.xml"
    
    print("=" * 60)
    print("🚀 APPTRONIK APOLLO HUMANOID ROBOT DEMO")
    print("=" * 60)
    print(f"Loading Apollo robot model from: {model_path}")
    
    # Load the model from XML file
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)
    
    print(f"✅ Model loaded successfully!")
    print(f"📊 Robot Statistics:")
    print(f"   • Degrees of freedom: {model.nv}")
    print(f"   • Number of actuators: {model.nu}")
    print(f"   • Number of bodies: {model.nbody}")
    print(f"   • Number of joints: {model.njnt}")
    print(f"   • Number of sensors: {model.nsensor}")
    
    # Print joint names for reference
    print(f"\n🔧 Joint Names:")
    for i in range(model.njnt):
        joint_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_JOINT, i)
        if joint_name:
            print(f"   {i:2d}: {joint_name}")
    
    # Initialize the robot in the standing position using the predefined keyframe
    print(f"\n🤖 Initializing robot in standing position...")
    
    # Find the "stand" keyframe
    stand_keyframe_id = mujoco.mj_name2id(model, mujoco.mjtObj.mjOBJ_KEY, "stand")
    if stand_keyframe_id >= 0:
        # Reset to the standing keyframe
        mujoco.mj_resetDataKeyframe(model, data, stand_keyframe_id)
        print(f"✅ Robot set to 'stand' keyframe")
    else:
        # Fallback to default reset
        mujoco.mj_resetData(model, data)
        print(f"⚠️  'stand' keyframe not found, using default position")
    
    # Create a viewer and run the simulation
    print(f"\n🎬 Starting Apollo robot simulation...")
    print(f"   • Simulation will run for 60 seconds")
    print(f"   • The robot will demonstrate various movements")
    print(f"   • Close the viewer window to exit early")
    print("=" * 60)
    
    with mujoco.viewer.launch_passive(model, data) as viewer:
        # Simulation parameters
        start_time = time.time()
        simulation_duration = 60  # Run for 60 seconds
        
        # Movement phases
        phase_duration = 10  # Each phase lasts 10 seconds
        current_phase = 0
        phase_start_time = start_time
        
        while viewer.is_running() and time.time() - start_time < simulation_duration:
            current_time = time.time()
            elapsed_time = current_time - start_time
            phase_time = current_time - phase_start_time
            
            # Switch phases every 10 seconds
            if phase_time >= phase_duration:
                current_phase = (current_phase + 1) % 6
                phase_start_time = current_time
                phase_time = 0
                print(f"🔄 Switching to phase {current_phase + 1}")
            
            # Apply different movements based on the current phase
            if current_phase == 0:
                # Phase 1: Standing still (let the robot settle)
                pass
                
            elif current_phase == 1:
                # Phase 2: Gentle arm movements
                arm_amplitude = 0.3
                arm_freq = 0.5
                # Left arm
                data.ctrl[6] = arm_amplitude * np.sin(phase_time * arm_freq)  # l_shoulder_aa
                data.ctrl[7] = 0.2 * np.sin(phase_time * arm_freq * 1.2)     # l_shoulder_ie
                data.ctrl[8] = -0.2 + 0.3 * np.sin(phase_time * arm_freq)    # l_shoulder_fe
                data.ctrl[9] = -0.5 + 0.3 * np.sin(phase_time * arm_freq)    # l_elbow_fe
                
                # Right arm (opposite motion)
                data.ctrl[13] = -arm_amplitude * np.sin(phase_time * arm_freq)  # r_shoulder_aa
                data.ctrl[14] = -0.2 * np.sin(phase_time * arm_freq * 1.2)     # r_shoulder_ie
                data.ctrl[15] = -0.2 + 0.3 * np.sin(phase_time * arm_freq)     # r_shoulder_fe
                data.ctrl[16] = -0.5 + 0.3 * np.sin(phase_time * arm_freq)     # r_elbow_fe
                
            elif current_phase == 2:
                # Phase 3: Head movements
                data.ctrl[3] = 0.5 * np.sin(phase_time * 0.3)  # neck_yaw
                data.ctrl[4] = 0.2 * np.sin(phase_time * 0.4)  # neck_roll
                data.ctrl[5] = 0.1 * np.sin(phase_time * 0.5)  # neck_pitch
                
            elif current_phase == 3:
                # Phase 4: Torso movements
                data.ctrl[0] = 0.2 * np.sin(phase_time * 0.4)  # torso_yaw
                data.ctrl[1] = 0.1 * np.sin(phase_time * 0.6)  # torso_roll
                data.ctrl[2] = 0.2 * np.sin(phase_time * 0.5)  # torso_pitch
                
            elif current_phase == 4:
                # Phase 5: Leg weight shifting
                shift_amplitude = 0.1
                shift_freq = 0.3
                # Slight hip movements for weight shifting
                data.ctrl[20] = shift_amplitude * np.sin(phase_time * shift_freq)      # l_hip_ie
                data.ctrl[26] = -shift_amplitude * np.sin(phase_time * shift_freq)     # r_hip_ie
                data.ctrl[22] = 0.05 * np.sin(phase_time * shift_freq * 1.5)          # l_hip_fe
                data.ctrl[28] = 0.05 * np.sin(phase_time * shift_freq * 1.5)          # r_hip_fe
                
            elif current_phase == 5:
                # Phase 6: Combined movements - full body coordination
                t = phase_time
                # Arms
                data.ctrl[6] = 0.2 * np.sin(t * 0.4)   # l_shoulder_aa
                data.ctrl[8] = -0.1 + 0.2 * np.sin(t * 0.4)  # l_shoulder_fe
                data.ctrl[13] = -0.2 * np.sin(t * 0.4)  # r_shoulder_aa
                data.ctrl[15] = -0.1 + 0.2 * np.sin(t * 0.4)  # r_shoulder_fe
                
                # Head
                data.ctrl[3] = 0.3 * np.sin(t * 0.2)   # neck_yaw
                
                # Torso
                data.ctrl[0] = 0.1 * np.sin(t * 0.3)   # torso_yaw
                data.ctrl[2] = 0.1 * np.sin(t * 0.25)  # torso_pitch
            
            # Step the simulation
            mujoco.mj_step(model, data)
            viewer.sync()
            
            # Add small delay for real-time viewing
            time.sleep(0.01)
    
    print("\n🎉 Apollo simulation completed!")
    print("Thank you for watching the Apptronik Apollo humanoid robot demo!")

if __name__ == "__main__":
    main()
