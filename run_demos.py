#!/usr/bin/env python3
"""
MuJoCo Playground Demo Runner
Run various robot demonstrations from the MuJoCo Menagerie
"""

import os
import sys
import subprocess

def print_banner():
    print("=" * 70)
    print("🤖 MUJOCO PLAYGROUND - ROBOT DEMONSTRATIONS")
    print("=" * 70)
    print("Available demonstrations:")
    print()

def print_menu():
    demos = [
        ("1", "Apollo Humanoid Robot (Basic)", "mjpython run_apollo.py"),
        ("2", "Apollo Humanoid Robot (Enhanced)", "mjpython apollo_enhanced_demo.py"),
        ("3", "Apollo Robot (Viewer Only)", "mjpython -m mujoco.viewer --mjcf mujoco_menagerie/apptronik_apollo/scene.xml"),
        ("4", "Browse All Available Robots", "ls mujoco_menagerie/"),
        ("5", "Unitree G1 Humanoid", "mjpython -m mujoco.viewer --mjcf mujoco_menagerie/unitree_g1/scene.xml"),
        ("6", "Boston Dynamics Spot", "mjpython -m mujoco.viewer --mjcf mujoco_menagerie/boston_dynamics_spot/scene.xml"),
        ("7", "Franka Panda Arm", "mjpython -m mujoco.viewer --mjcf mujoco_menagerie/franka_emika_panda/scene.xml"),
        ("8", "Unitree Go2 Quadruped", "mjpython -m mujoco.viewer --mjcf mujoco_menagerie/unitree_go2/scene.xml"),
    ]
    
    for num, name, _ in demos:
        print(f"  {num}. {name}")
    
    print(f"  q. Quit")
    print()
    return demos

def run_command(command):
    """Run a command and handle errors gracefully"""
    try:
        print(f"🚀 Running: {command}")
        print("-" * 50)
        
        if command.startswith("ls "):
            # Handle directory listing specially
            result = subprocess.run(command.split(), capture_output=True, text=True)
            if result.returncode == 0:
                print("Available robots in MuJoCo Menagerie:")
                print()
                for line in result.stdout.strip().split('\n'):
                    if os.path.isdir(f"mujoco_menagerie/{line}") and not line.startswith('.'):
                        print(f"  📁 {line}")
                print()
                print("To run any robot, use:")
                print("  mjpython -m mujoco.viewer --mjcf mujoco_menagerie/ROBOT_NAME/scene.xml")
            else:
                print(f"Error: {result.stderr}")
        else:
            # Run the command normally
            result = subprocess.run(command, shell=True)
            if result.returncode != 0:
                print(f"⚠️  Command exited with code {result.returncode}")
        
        print("-" * 50)
        print("✅ Demo completed!")
        
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Error running demo: {e}")

def check_requirements():
    """Check if required files and dependencies are available"""
    print("🔍 Checking requirements...")
    
    # Check if MuJoCo Menagerie is available
    if not os.path.exists("mujoco_menagerie"):
        print("❌ MuJoCo Menagerie not found!")
        print("   Please run: git clone https://github.com/google-deepmind/mujoco_menagerie.git")
        return False
    
    # Check if Apollo model is available
    if not os.path.exists("mujoco_menagerie/apptronik_apollo/scene.xml"):
        print("❌ Apollo robot model not found!")
        return False
    
    # Check if mjpython is available
    mjpython_paths = [
        "mjpython",
        "/Library/Frameworks/Python.framework/Versions/3.11/bin/mjpython",
        "/usr/local/bin/mjpython"
    ]

    mjpython_found = False
    for mjpython_path in mjpython_paths:
        try:
            result = subprocess.run([mjpython_path, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                mjpython_found = True
                break
        except FileNotFoundError:
            continue

    if not mjpython_found:
        print("❌ mjpython not found! Please install MuJoCo.")
        print("   Tried paths:", mjpython_paths)
        return False
    
    print("✅ All requirements satisfied!")
    return True

def main():
    print_banner()
    
    if not check_requirements():
        print("\n❌ Requirements not met. Please install missing dependencies.")
        sys.exit(1)
    
    print()
    
    while True:
        demos = print_menu()
        
        try:
            choice = input("Select a demo (1-8, q to quit): ").strip().lower()
            
            if choice == 'q' or choice == 'quit':
                print("👋 Goodbye!")
                break
            
            # Find the selected demo
            selected_demo = None
            for num, name, command in demos:
                if choice == num:
                    selected_demo = (name, command)
                    break
            
            if selected_demo:
                name, command = selected_demo
                print(f"\n🎬 Starting: {name}")
                run_command(command)
                print()
            else:
                print("❌ Invalid choice. Please select a number from 1-8 or 'q' to quit.")
                print()
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except EOFError:
            print("\n👋 Goodbye!")
            break

if __name__ == "__main__":
    main()
